<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="14">
            <item index="0" class="java.lang.String" itemvalue="tensorflow-estimator" />
            <item index="1" class="java.lang.String" itemvalue="decorator" />
            <item index="2" class="java.lang.String" itemvalue="dataclasses" />
            <item index="3" class="java.lang.String" itemvalue="networkx" />
            <item index="4" class="java.lang.String" itemvalue="six" />
            <item index="5" class="java.lang.String" itemvalue="joblib" />
            <item index="6" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="7" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="8" class="java.lang.String" itemvalue="imageio" />
            <item index="9" class="java.lang.String" itemvalue="PyWavelets" />
            <item index="10" class="java.lang.String" itemvalue="zipp" />
            <item index="11" class="java.lang.String" itemvalue="grpcio" />
            <item index="12" class="java.lang.String" itemvalue="Markdown" />
            <item index="13" class="java.lang.String" itemvalue="e2cnn-py36" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredIdentifiers">
        <list>
          <option value="random.random.randint" />
          <option value="cv2.imgread" />
          <option value="cv2.imread.grayscale" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>